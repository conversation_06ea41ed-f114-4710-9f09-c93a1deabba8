import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:app_settings/app_settings.dart';
import '../main.dart';
import '../models/music_track.dart';
import '../models/exercise_history.dart';
import '../database/database_helper.dart';

// Enum for notification permission status
enum NotificationPermissionStatus {
  granted,
  denied,
  permanentlyDenied,
  notDetermined,
}

// Define the BreathingTechnique class outside AppState
class BreathingTechnique {
  final String name;
  final String description;
  final List<String> benefits;
  final List<String> instructions;
  final Map<String, int> pattern; // e.g., {"inhale": 4, "hold": 7, "exhale": 8}
  final int cycles; // number of repetitions
  final String iconName; // For FeatherIcons or similar
  final Color color; // Unique color for the button
  final int requiredPoints; // Points required to unlock this technique
  final int
      requiredChallenges; // Number of challenges required to unlock this technique

  const BreathingTechnique({
    required this.name,
    required this.description,
    required this.benefits,
    required this.instructions,
    required this.pattern,
    required this.cycles,
    required this.iconName,
    required this.color,
    this.requiredPoints = 0, // Default to 0 points (unlocked from start)
    this.requiredChallenges =
        0, // Default to 0 challenges (unlocked from start)
  });
}

class AppState extends ChangeNotifier {
  int _points = 0;
  bool _notificationsEnabled = true;
  bool _hapticFeedbackEnabled = true; // Haptic feedback for breathing exercises
  int _dailyStreak = 0; // Track consecutive days
  String _lastExerciseDate = ''; // Track last exercise date
  int _exercisesCompleted = 0; // Track total completed exercises
  String? _reminderTime; // Daily reminder time
  int _dailyExerciseCount = 0; // Track exercises completed per day

  // Notification permission status tracking
  NotificationPermissionStatus _notificationPermissionStatus =
      NotificationPermissionStatus.notDetermined;

  // Background music properties
  bool _backgroundMusicEnabled = false;
  String _selectedMusicTrackId = 'calm_lake'; // Default music track
  List<MusicTrack> _musicTracks = MusicTrack.predefinedTracks;
  AudioPlayer? _musicPlayer;

  // List of completed challenges
  List<String> _completedChallenges = [];

  // Challenge definitions with descriptions and requirements
  final Map<String, String> _challengeDescriptions = {
    'streak_7': 'رکورد ۷ روزه',
    'streak_30': 'رکورد ۳۰ روزه',
    'all_exercises': 'کاوشگر تمرینات',
    'exercises_10': '۱۰ تمرین تکمیل شده',
    'exercises_50': '۵۰ تمرین تکمیل شده',
    'points_250': 'کسب ۲۵۰ امتیاز',
    'daily_challenge': 'تنفس عمیق روزانه',
    'weekly_challenge': 'آرامش هفتگی',
  };

  // Challenge requirements and tracking info
  final Map<String, Map<String, dynamic>> _challengeRequirements = {
    'streak_7': {'type': 'streak', 'target': 7},
    'streak_30': {'type': 'streak', 'target': 30},
    'all_exercises': {
      'type': 'exercises',
      'target': -1
    }, // Special case: all techniques
    'exercises_10': {'type': 'count', 'target': 10},
    'exercises_50': {'type': 'count', 'target': 50},
    'points_250': {'type': 'points', 'target': 250},
    'daily_challenge': {
      'type': 'daily',
      'target': 1,
      'description': 'امروز یک تمرین تنفس عمیق تکمیل کنید'
    },
    'weekly_challenge': {
      'type': 'weekly',
      'target': 5,
      'description': 'این هفته ۵ تمرین تنفسی تکمیل کنید'
    },
  };

  // Track daily and weekly challenge progress
  int _dailyChallengeProgress = 0;
  int _weeklyChallengeProgress = 0;
  String _dailyChallengeDate = '';
  String _weeklyChallengeStartDate = '';
  String _weeklyChallengeLastUpdate = '';

  // Add the list of breathing techniques with points and challenges requirements
  final List<BreathingTechnique> _breathingTechniques = [
    const BreathingTechnique(
      name: "تنفس شکمی",
      description:
          "تکنیک ساده‌ای که بر تنفس عمیق به داخل دیافراگم تمرکز دارد تا آرامش و جریان اکسیژن را تقویت کند.",
      benefits: ["کاهش استرس", "کاهش ضربان قلب", "بهبود تمرکز"],
      instructions: [
        "راحت بنشینید یا دراز بکشید.",
        "یک دست را روی سینه و دست دیگر را روی شکم قرار دهید.",
        "۴ ثانیه از بینی عمیق نفس بکشید و احساس کنید شکمتان بالا می‌آید.",
        "۶ ثانیه آهسته از دهان نفس را خارج کنید و احساس کنید شکمتان پایین می‌آید.",
      ],
      pattern: {"inhale": 4, "exhale": 6},
      cycles: 5,
      iconName: "wind", // Example icon name
      color: Color(0xFF4682B4), // Steel Blue
      requiredPoints: 0, // Available from start (0 points)
      requiredChallenges: 0, // Available from start (0 challenges)
    ),
    const BreathingTechnique(
      name: "تنفس لب جمع شده",
      description:
          "تکنیکی برای کند کردن تنفس و بهبود تبادل اکسیژن که اغلب برای بیماری‌های ریوی استفاده می‌شود.",
      benefits: ["بهبود کارایی تنفس", "کاهش تنگی نفس", "آرام کردن ذهن"],
      instructions: [
        "راحت بنشینید یا بایستید.",
        "۲ ثانیه از بینی نفس بکشید.",
        "لب‌هایتان را جمع کنید (مثل سوت زدن) و ۴ ثانیه آهسته نفس را خارج کنید.",
      ],
      pattern: {"inhale": 2, "exhale": 4},
      cycles: 8,
      iconName: "sunrise", // Example icon name
      color: Color(0xFFFFA07A), // Light Salmon
      requiredPoints: 0, // Available from start (0 points)
      requiredChallenges: 0, // Available from start (0 challenges)
    ),
    const BreathingTechnique(
      name: "تنفس ۴-۷-۸",
      description:
          "تکنیک آرام‌بخشی که برای کاهش اضطراب و کمک به خواب از طریق تنظیم تنفس طراحی شده است.",
      benefits: ["تقویت آرامش", "کاهش اضطراب", "کمک به خواب"],
      instructions: [
        "با کمر صاف بنشینید.",
        "۴ ثانیه آرام از بینی نفس بکشید.",
        "۷ ثانیه نفستان را نگه دارید.",
        "۸ ثانیه کاملاً از دهان نفس را خارج کنید و صدای هیس درآورید.",
      ],
      pattern: {"inhale": 4, "hold": 7, "exhale": 8},
      cycles: 4,
      iconName: "moon", // Example icon name
      color: Color(0xFF5F9EA0), // Cadet Blue
      requiredPoints: 100, // Available for Calm Seeker (100+ points)
      requiredChallenges: 0, // No challenges required
    ),
    const BreathingTechnique(
      name: "تنفس جعبه‌ای",
      description:
          "تکنیک ساختاریافته‌ای که توسط ورزشکاران و متخصصان برای تقویت تمرکز و آرام کردن ذهن استفاده می‌شود.",
      benefits: ["بهبود تمرکز", "کاهش استرس", "تعادل احساسات"],
      instructions: [
        "صاف بنشینید و شانه‌هایتان را شل کنید.",
        "۴ ثانیه از بینی نفس بکشید.",
        "۴ ثانیه نفستان را نگه دارید.",
        "۴ ثانیه از دهان نفس را خارج کنید.",
        "دوباره ۴ ثانیه نفستان را نگه دارید.",
      ],
      pattern: {
        "inhale": 4,
        "hold1": 4,
        "exhale": 4,
        "hold2": 4
      }, // Renamed holds for uniqueness
      cycles: 6,
      iconName: "square", // Example icon name
      color: Color(0xFF3CB371), // Medium Sea Green
      requiredPoints: 100, // Available for Calm Seeker (100+ points)
      requiredChallenges: 0, // No challenges required
    ),
    const BreathingTechnique(
      name: "تنفس متناوب سوراخ بینی",
      description:
          "تمرین یوگایی که نیمکره‌های چپ و راست مغز را از طریق تنفس متناوب سوراخ بینی متعادل می‌کند.",
      benefits: ["تقویت وضوح ذهنی", "کاهش استرس", "تعادل انرژی"],
      instructions: [
        "راحت با ستون فقرات صاف بنشینید.",
        "سوراخ بینی راست را با انگشت شست ببندید و ۴ ثانیه از سوراخ بینی چپ نفس بکشید.",
        "سوراخ بینی چپ را با انگشت حلقه ببندید و ۴ ثانیه نگه دارید.",
        "سوراخ بینی راست را باز کنید و ۶ ثانیه نفس را خارج کنید.",
        "با تعویض سوراخ بینی تکرار کنید.",
      ],
      pattern: {
        "inhale": 4,
        "hold": 4,
        "exhale": 6
      }, // Note: UI needs to guide nostril switching
      cycles: 5, // Per side
      iconName: "git-branch", // Example icon name
      color: Color(0xFF9370DB), // Medium Purple
      requiredPoints: 250, // Available for Breath Master (250+ points)
      requiredChallenges: 2, // Requires 2 completed challenges
    ),
    const BreathingTechnique(
      name: "پرانایاما زنبوری (بهرامری)",
      description:
          "تکنیک تنفس زمزمه‌ای که سیستم عصبی را با ارتعاش صدا آرام می‌کند.",
      benefits: ["کاهش تنش", "کاهش خشم", "بهبود کیفیت خواب"],
      instructions: [
        "در جای آرامی با چشمان بسته بنشینید.",
        "۴ ثانیه عمیق از بینی نفس بکشید.",
        "گوش‌هایتان را با انگشت شست ببندید و ۶ ثانیه با صدای زمزمه زنبور نفس را خارج کنید.",
      ],
      pattern: {
        "inhale": 4,
        "exhale": 6
      }, // Note: UI needs to remind user to hum
      cycles: 7,
      iconName: "headphones", // Example icon name
      color: Color(0xFFCD853F), // Peru
      requiredPoints: 250, // Available for Breath Master (250+ points)
      requiredChallenges: 2, // Requires 2 completed challenges
    ),
    const BreathingTechnique(
      name: "پرانایاما کپال بهاتی",
      description:
          "تکنیک انرژی‌بخشی که شامل بازدم‌های قوی برای پاکسازی بدن و ذهن است.",
      benefits: ["تقویت انرژی", "بهبود هضم", "افزایش هوشیاری ذهنی"],
      instructions: [
        "چهارزانو با ستون فقرات صاف بنشینید.",
        "۲ ثانیه عمیق از بینی نفس بکشید.",
        "با منقبض کردن شکم، ۱ ثانیه قوی از بینی نفس را خارج کنید.",
        "اجازه دهید دم بطور غیرفعال اتفاق بیفتد.",
      ],
      pattern: {
        "inhale": 2,
        "exhale": 1
      }, // Note: Exhale is forceful, passive inhale
      cycles: 20,
      iconName: "zap", // Example icon name
      color: Color(0xFFFF6347), // Tomato
      requiredPoints: 500, // Available for Breath Legend (500+ points)
      requiredChallenges: 4, // Requires 4 completed challenges
    ),
  ];

  // Getter methods
  int get points => _points;
  int get dailyStreak => _dailyStreak;
  String get lastExerciseDate => _lastExerciseDate;
  List<String> get completedChallenges => _completedChallenges;
  Map<String, String> get challengeDescriptions => _challengeDescriptions;
  // Returns true only when both app setting is enabled AND OS permissions are granted
  bool get notificationsEnabled =>
      _notificationsEnabled &&
      _notificationPermissionStatus == NotificationPermissionStatus.granted;

  // Internal app setting (separate from OS permissions)
  bool get notificationsEnabledInApp => _notificationsEnabled;
  bool get hapticFeedbackEnabled => _hapticFeedbackEnabled;
  int get exercisesCompleted => _exercisesCompleted;
  Map<String, Map<String, dynamic>> get challengeRequirements =>
      _challengeRequirements;
  NotificationPermissionStatus get notificationPermissionStatus =>
      _notificationPermissionStatus;

  String? get reminderTime => _reminderTime;
  int get dailyChallengeProgress => _dailyChallengeProgress;
  int get weeklyChallengeProgress => _weeklyChallengeProgress;
  int get dailyExerciseCount => _dailyExerciseCount;

  // Music-related getters
  bool get backgroundMusicEnabled => _backgroundMusicEnabled;
  String get selectedMusicTrackId => _selectedMusicTrackId;
  List<MusicTrack> get musicTracks => _musicTracks;
  MusicTrack get selectedMusicTrack => _musicTracks.firstWhere(
        (track) => track.id == _selectedMusicTrackId,
        orElse: () => _musicTracks.first,
      );

  // Getter for breathing techniques
  List<BreathingTechnique> get breathingTechniques => _breathingTechniques;

  // Check if a breathing technique is unlocked based on points and challenges
  bool isTechniqueUnlocked(BreathingTechnique technique) {
    return _points >= technique.requiredPoints &&
        _completedChallenges.length >= technique.requiredChallenges;
  }

  // Get unlock requirements for a technique
  String getUnlockRequirements(BreathingTechnique technique) {
    if (technique.requiredPoints > 0 && technique.requiredChallenges > 0) {
      return 'نیاز به ${technique.requiredPoints} امتیاز و ${technique.requiredChallenges} چالش';
    } else if (technique.requiredPoints > 0) {
      return 'نیاز به ${technique.requiredPoints} امتیاز';
    } else {
      return 'از ابتدا در دسترس';
    }
  }

  // Constructor to load saved preferences
  AppState() {
    _loadFromPrefs();
    // Check notification permission status on initialization
    checkAndUpdateNotificationPermissionStatus();
  }

  // Check and update notification permission status
  Future<void> checkAndUpdateNotificationPermissionStatus() async {
    try {
      if (Platform.isAndroid) {
        final androidImplementation = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidImplementation != null) {
          final bool? granted =
              await androidImplementation.areNotificationsEnabled();
          if (granted == true) {
            _notificationPermissionStatus =
                NotificationPermissionStatus.granted;
          } else {
            // For Android, we need to check if we can request permissions
            // If we can't request, it means permanently denied
            _notificationPermissionStatus = NotificationPermissionStatus.denied;
          }
        }
      } else if (Platform.isIOS) {
        final iOSImplementation = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();

        if (iOSImplementation != null) {
          final bool? granted = await iOSImplementation.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

          if (granted == true) {
            _notificationPermissionStatus =
                NotificationPermissionStatus.granted;
          } else {
            _notificationPermissionStatus = NotificationPermissionStatus.denied;
          }
        }
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error checking notification permissions: $e');
      _notificationPermissionStatus =
          NotificationPermissionStatus.notDetermined;
      notifyListeners();
    }
  }

  // Request notification permissions
  Future<bool> requestNotificationPermissions() async {
    try {
      if (Platform.isAndroid) {
        final androidImplementation = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidImplementation != null) {
          final bool? granted =
              await androidImplementation.requestNotificationsPermission();
          if (granted == true) {
            _notificationPermissionStatus =
                NotificationPermissionStatus.granted;
            notifyListeners();
            return true;
          } else {
            _notificationPermissionStatus = NotificationPermissionStatus.denied;
            notifyListeners();
            return false;
          }
        }
      } else if (Platform.isIOS) {
        final iOSImplementation = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();

        if (iOSImplementation != null) {
          final bool? granted = await iOSImplementation.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

          if (granted == true) {
            _notificationPermissionStatus =
                NotificationPermissionStatus.granted;
            notifyListeners();
            return true;
          } else {
            _notificationPermissionStatus = NotificationPermissionStatus.denied;
            notifyListeners();
            return false;
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
      _notificationPermissionStatus = NotificationPermissionStatus.denied;
      notifyListeners();
      return false;
    }
  }

  // Open app settings for notification permissions
  Future<void> openNotificationSettings() async {
    try {
      await AppSettings.openAppSettings(type: AppSettingsType.notification);
    } catch (e) {
      debugPrint('Error opening notification settings: $e');
      // Fallback to general app settings
      try {
        await AppSettings.openAppSettings();
      } catch (fallbackError) {
        debugPrint('Error opening app settings: $fallbackError');
      }
    }
  }

  // Add points when exercise is completed
  void addPoints(int value) {
    // Check if it's a new day and reset daily exercise count if needed
    _checkAndResetDailyExerciseCount();

    // Calculate points based on daily exercise count
    int pointsToAdd = 0;

    // Award points based on daily exercise count (first 3 exercises only)
    if (_dailyExerciseCount == 0) {
      pointsToAdd = 15; // First exercise: 15 points
    } else if (_dailyExerciseCount == 1) {
      pointsToAdd = 10; // Second exercise: 10 points
    } else if (_dailyExerciseCount == 2) {
      pointsToAdd = 5; // Third exercise: 5 points
    } else {
      pointsToAdd = 0; // Additional exercises: 0 points
    }

    // Increment daily exercise count
    _dailyExerciseCount++;

    // Add points to total
    _points += pointsToAdd;

    // Increment completed exercises count
    _exercisesCompleted++;

    // Check exercise count challenges
    _checkExerciseCountChallenges();

    // Check points-based challenges
    _checkPointsChallenges();

    // Update daily and weekly challenges
    _updateDailyChallenge();
    _updateWeeklyChallenge();

    _saveToPrefs();
    notifyListeners();
  }

  // Check if it's a new day and reset daily exercise count if needed
  void _checkAndResetDailyExerciseCount() {
    final today = DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD

    if (_lastExerciseDate != today) {
      _dailyExerciseCount = 0;
    }
  }

  // Update daily challenge progress
  void _updateDailyChallenge() {
    final today = DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD

    // Reset progress if it's a new day
    if (_dailyChallengeDate != today) {
      _dailyChallengeProgress = 0;
      _dailyChallengeDate = today;
    }

    // Increment progress
    _dailyChallengeProgress++;

    // Check if daily challenge is completed
    if (_dailyChallengeProgress >= 1 &&
        !_completedChallenges.contains('daily_challenge')) {
      awardChallengePoints('daily_challenge');
    }
  }

  // Update weekly challenge progress
  void _updateWeeklyChallenge() {
    try {
      final now = DateTime.now();
      final today = now.toIso8601String().split('T')[0];

      // Safely calculate week start
      DateTime weekStart = now.subtract(Duration(days: now.weekday - 1));
      final currentWeekStart = weekStart.toIso8601String().split('T')[0];

      // Validate stored date before comparison
      if (_weeklyChallengeStartDate.isEmpty ||
          _weeklyChallengeStartDate != currentWeekStart) {
        _weeklyChallengeProgress = 0;
        _weeklyChallengeStartDate = currentWeekStart;
        _weeklyChallengeLastUpdate = '';
      }

      // Only increment if we haven't already counted today
      if (_weeklyChallengeLastUpdate != today) {
        _weeklyChallengeProgress++;
        _weeklyChallengeLastUpdate = today;
      }

      // Check if weekly challenge is completed
      if (_weeklyChallengeProgress >= 5 &&
          !_completedChallenges.contains('weekly_challenge')) {
        awardChallengePoints('weekly_challenge');
      }
    } catch (e) {
      // Prevent crashes by catching any exceptions
    }
  }

  // List to store newly unlocked techniques
  List<BreathingTechnique> _newlyUnlockedTechniques = [];

  // Getter for newly unlocked techniques
  List<BreathingTechnique> get newlyUnlockedTechniques =>
      _newlyUnlockedTechniques;

  // Clear the list of newly unlocked techniques
  void clearNewlyUnlockedTechniques() {
    _newlyUnlockedTechniques = [];
    notifyListeners();
  }

  // Get user title based on points and challenges
  String getUserTitle() {
    if (_points >= 500 && _completedChallenges.length >= 4) {
      return "مرشد جان";
    } else if (_points >= 250 && _completedChallenges.length >= 2) {
      return "استاد درون";
    } else if (_points >= 100) {
      return "جویای سکون";
    } else {
      return "رهرو آرامش";
    }
  }

  // Get profile image path based on user level
  String getProfileImagePath() {
    if (_points >= 500 && _completedChallenges.length >= 4) {
      return "assets/images/profiles/profile_level4.webp"; // Breath Legend
    } else if (_points >= 250 && _completedChallenges.length >= 2) {
      return "assets/images/profiles/profile_level3.webp"; // Breath Master
    } else if (_points >= 100) {
      return "assets/images/profiles/profile_level2.webp"; // Calm Seeker
    } else {
      return "assets/images/profiles/profile_level1.webp"; // Breath Novice
    }
  }

  // Get profile image path for a specific level
  String getProfileImagePathForLevel(int level) {
    if (level >= 5) {
      return "assets/images/profiles/profile_level4.webp"; // Breath Legend
    } else if (level >= 2) {
      return "assets/images/profiles/profile_level3.webp"; // Breath Master
    } else if (level >= 1) {
      return "assets/images/profiles/profile_level2.webp"; // Calm Seeker
    } else {
      return "assets/images/profiles/profile_level1.webp"; // Breath Novice
    }
  }

  // Update daily streak when exercise is completed
  void updateDailyStreak() {
    final today =
        DateTime.now().toIso8601String().split('T')[0]; // Just get YYYY-MM-DD

    if (_lastExerciseDate.isEmpty) {
      // First exercise ever
      _dailyStreak = 1;
    } else if (_lastExerciseDate == today) {
      // Already did an exercise today, don't increment streak
      return;
    } else {
      final lastDate = DateTime.parse(_lastExerciseDate);
      final currentDate = DateTime.parse(today);
      final difference = currentDate.difference(lastDate).inDays;

      if (difference == 1) {
        // Consecutive day
        _dailyStreak++;

        // Check streak challenges
        _checkStreakChallenges();
      } else if (difference > 1) {
        // Streak broken
        _dailyStreak = 1;
      }
    }

    _lastExerciseDate = today;
    _saveToPrefs();
    notifyListeners();
  }

  // Award points for completing challenges
  void awardChallengePoints(String challengeId) {
    // Only award points if this is a new challenge completion
    if (_completedChallenges.contains(challengeId)) {
      return;
    }

    // Add challenge to completed list
    _completedChallenges.add(challengeId);

    // Award points based on challenge difficulty
    int pointsToAward = 0;

    // Simple challenges
    if (challengeId == 'daily_challenge' || challengeId == 'exercises_10') {
      pointsToAward = 20; // Simple challenges: 20 points
    }
    // Medium challenges
    else if (challengeId == 'streak_7' ||
        challengeId == 'points_250' ||
        challengeId == 'weekly_challenge') {
      pointsToAward = 50; // Medium challenges: 50 points
    }
    // Hard challenges
    else if (challengeId == 'streak_30' ||
        challengeId == 'exercises_50' ||
        challengeId == 'all_exercises') {
      pointsToAward = 100; // Hard challenges: 100 points
    }

    // Add points to total
    _points += pointsToAward;

    _saveToPrefs();
    notifyListeners();
  }

  // Check streak-based challenges
  void _checkStreakChallenges() {
    if (_dailyStreak >= 7 && !_completedChallenges.contains('streak_7')) {
      awardChallengePoints('streak_7');
    }
    if (_dailyStreak >= 30 && !_completedChallenges.contains('streak_30')) {
      awardChallengePoints('streak_30');
    }
  }

  // Check exercise count challenges
  void _checkExerciseCountChallenges() {
    if (_exercisesCompleted >= 10 &&
        !_completedChallenges.contains('exercises_10')) {
      awardChallengePoints('exercises_10');
    }
    if (_exercisesCompleted >= 50 &&
        !_completedChallenges.contains('exercises_50')) {
      awardChallengePoints('exercises_50');
    }
  }

  // Check points-based challenges
  void _checkPointsChallenges() {
    if (_points >= 250 && !_completedChallenges.contains('points_250')) {
      awardChallengePoints('points_250');
    }
  }

  // Check if all exercises have been completed
  Future<void> checkAllExercisesChallenge(String exerciseTitle) async {
    // Set to track unique completed exercises
    final prefs = await SharedPreferences.getInstance();
    Set<String> completedExercises =
        Set<String>.from(prefs.getStringList('completedExerciseTypes') ?? []);

    // Add current exercise to set
    completedExercises.add(exerciseTitle);

    // Save updated set to preferences
    await prefs.setStringList(
        'completedExerciseTypes', completedExercises.toList());

    // If all breathing techniques have been completed, add the challenge
    if (completedExercises.length >= _breathingTechniques.length &&
        !_completedChallenges.contains('all_exercises')) {
      awardChallengePoints('all_exercises');
    }
  }

  // Save completed exercise to history
  Future<void> saveExerciseToHistory(BreathingTechnique technique) async {
    try {
      // Create exercise history entry
      final exerciseHistory = ExerciseHistory(
        technique: technique.name,
        date: DateTime.now(),
        color: technique.color,
        iconName: technique.iconName,
      );

      // Save to database
      await DatabaseHelper.instance
          .saveExerciseHistory(exerciseHistory.toMap());

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      // Handle error silently in production
      debugPrint('Error saving exercise history: $e');
    }
  }

  // Get recent exercise history
  Future<List<ExerciseHistory>> getRecentExerciseHistory(
      {int limit = 3}) async {
    try {
      // Get history from database
      final historyMaps =
          await DatabaseHelper.instance.getExerciseHistory(limit: limit);

      // Convert maps to ExerciseHistory objects
      return historyMaps.map((map) => ExerciseHistory.fromMap(map)).toList();
    } catch (e) {
      // Handle error silently in production
      debugPrint('Error getting exercise history: $e');
      return [];
    }
  }

  // Get all exercise history
  Future<List<ExerciseHistory>> getAllExerciseHistory() async {
    try {
      // Get all history from database
      final historyMaps = await DatabaseHelper.instance.getAllExerciseHistory();

      // Convert maps to ExerciseHistory objects
      return historyMaps.map((map) => ExerciseHistory.fromMap(map)).toList();
    } catch (e) {
      // Handle error silently in production
      debugPrint('Error getting all exercise history: $e');
      return [];
    }
  }

  // Toggle notifications with permission handling
  Future<bool> toggleNotifications() async {
    if (_notificationsEnabled) {
      // User is turning off notifications
      _notificationsEnabled = false;
      _saveToPrefs();
      notifyListeners();
      return true;
    } else {
      // User is trying to turn on notifications
      // Check current permission status first
      await checkAndUpdateNotificationPermissionStatus();

      switch (_notificationPermissionStatus) {
        case NotificationPermissionStatus.granted:
          // Permission already granted, enable notifications
          _notificationsEnabled = true;
          _saveToPrefs();
          notifyListeners();
          return true;

        case NotificationPermissionStatus.notDetermined:
          // Request permission
          final granted = await requestNotificationPermissions();
          if (granted) {
            _notificationsEnabled = true;
            _saveToPrefs();
            notifyListeners();
            return true;
          } else {
            // Permission denied, keep notifications disabled
            _notificationsEnabled = false;
            _saveToPrefs();
            notifyListeners();
            return false;
          }

        case NotificationPermissionStatus.denied:
        case NotificationPermissionStatus.permanentlyDenied:
          // Permission denied, keep notifications disabled
          _notificationsEnabled = false;
          _saveToPrefs();
          notifyListeners();
          return false;
      }
    }
  }

  // Toggle haptic feedback
  void toggleHapticFeedback() {
    _hapticFeedbackEnabled = !_hapticFeedbackEnabled;
    _saveToPrefs();
    notifyListeners();
  }

  // Set reminder time
  void setReminderTime(String time) {
    _reminderTime = time;
    _saveToPrefs();
    notifyListeners();
  }

  // Toggle background music
  void toggleBackgroundMusic() {
    _backgroundMusicEnabled = !_backgroundMusicEnabled;

    if (!_backgroundMusicEnabled && _musicPlayer != null) {
      _pauseBackgroundMusic();
    } else if (_backgroundMusicEnabled && _musicPlayer != null) {
      _resumeBackgroundMusic();
    }

    _saveToPrefs();
    notifyListeners();
  }

  // Set selected music track
  void setSelectedMusicTrack(String trackId) async {
    if (_selectedMusicTrackId == trackId) return;

    _selectedMusicTrackId = trackId;

    // If music is currently playing, stop and restart with new track
    if (_backgroundMusicEnabled && _musicPlayer != null) {
      await _stopBackgroundMusic();
      await _playBackgroundMusic();
    }

    _saveToPrefs();
    notifyListeners();
  }

  // Initialize music player
  Future<void> initMusicPlayer() async {
    _musicPlayer ??= AudioPlayer();

    // Update track download status
    _updateMusicTrackStatus();
  }

  // Update music track status (check which ones are downloaded)
  Future<void> _updateMusicTrackStatus() async {
    List<MusicTrack> updatedTracks = [];

    for (var track in _musicTracks) {
      // Check if the track is already downloaded
      if (track.localPath != null && track.isDownloaded) {
        // Verify the file still exists
        final file = File(track.localPath!);
        if (await file.exists()) {
          updatedTracks.add(track);
          continue;
        }
      }

      // Track not downloaded or file missing, add the original
      updatedTracks.add(track);
    }

    _musicTracks = updatedTracks;
    notifyListeners();
  }

  // Download a music track
  Future<void> downloadMusicTrack(String trackId) async {
    final trackIndex = _musicTracks.indexWhere((track) => track.id == trackId);
    if (trackIndex == -1) return;

    final track = _musicTracks[trackIndex];
    final updatedTrack = await MusicTrack.downloadTrack(track);

    // Update the track in the list
    _musicTracks[trackIndex] = updatedTrack;

    notifyListeners();
  }

  // Play background music
  Future<void> playBackgroundMusic() async {
    if (!_backgroundMusicEnabled) return;

    await _playBackgroundMusic();
  }

  // Internal method to play background music
  Future<void> _playBackgroundMusic() async {
    if (_musicPlayer == null) {
      await initMusicPlayer();
    }

    try {
      final track = selectedMusicTrack;

      // If track is not downloaded, download it first
      if (!track.isDownloaded || track.localPath == null) {
        final updatedTrack = await MusicTrack.downloadTrack(track);
        final trackIndex = _musicTracks.indexWhere((t) => t.id == track.id);
        if (trackIndex != -1) {
          _musicTracks[trackIndex] = updatedTrack;
        }

        // If download failed, try to play from URL
        if (!updatedTrack.isDownloaded) {
          await _musicPlayer!.play(UrlSource(track.url));
        } else {
          await _musicPlayer!.play(DeviceFileSource(updatedTrack.localPath!));
        }
      } else {
        // Play from local file
        await _musicPlayer!.play(DeviceFileSource(track.localPath!));
      }

      // Set to loop
      await _musicPlayer!.setReleaseMode(ReleaseMode.loop);

      // Set volume
      await _musicPlayer!.setVolume(0.5);
    } catch (e) {
      debugPrint('Error playing background music: $e');
    }
  }

  // Pause background music
  Future<void> pauseBackgroundMusic() async {
    await _pauseBackgroundMusic();
  }

  // Resume background music
  Future<void> resumeBackgroundMusic() async {
    if (!_backgroundMusicEnabled) return;

    await _resumeBackgroundMusic();
  }

  // Stop background music
  Future<void> stopBackgroundMusic() async {
    await _stopBackgroundMusic();
  }

  // Internal method to pause background music (keeps position)
  Future<void> _pauseBackgroundMusic() async {
    if (_musicPlayer != null) {
      try {
        await _musicPlayer!.pause();
        debugPrint('Background music paused successfully');
      } catch (e) {
        debugPrint('Error pausing background music: $e');
      }
    }
  }

  // Internal method to resume background music from current position
  Future<void> _resumeBackgroundMusic() async {
    if (_musicPlayer != null) {
      try {
        await _musicPlayer!.resume();
        debugPrint('Background music resumed successfully');
      } catch (e) {
        debugPrint('Error resuming background music: $e');
        // If resume fails, try to play from current position
        try {
          await _playBackgroundMusic();
        } catch (playError) {
          debugPrint(
              'Error playing background music after resume failure: $playError');
        }
      }
    } else {
      await _playBackgroundMusic();
    }
  }

  // Internal method to stop background music
  Future<void> _stopBackgroundMusic() async {
    if (_musicPlayer != null) {
      try {
        await _musicPlayer!.stop();
        debugPrint('Background music stopped successfully');
      } catch (e) {
        debugPrint('Error stopping background music: $e');
        // Try to release resources if stop fails
        try {
          await _musicPlayer!.release();
        } catch (releaseError) {
          debugPrint('Error releasing music player: $releaseError');
        }
      }
    }
  }

  // Helper method for testing
  void setMusicPlayerForTesting(AudioPlayer player) {
    _musicPlayer = player;
  }

  // Schedule notification
  Future<void> scheduleNotification() async {
    // Check if notifications are enabled and permission is granted
    if (!_notificationsEnabled ||
        _reminderTime == null ||
        _notificationPermissionStatus != NotificationPermissionStatus.granted) {
      return;
    }

    try {
      // Cancel any existing notifications
      await flutterLocalNotificationsPlugin.cancelAll();

      // Parse reminder time
      final timeParts = _reminderTime!.split(' ');
      if (timeParts.length != 2) return;

      final hourMinute = timeParts[0].split(':');
      if (hourMinute.length != 2) return;

      int hour = int.tryParse(hourMinute[0]) ?? 0;
      final int minute = int.tryParse(hourMinute[1]) ?? 0;

      // Convert to 24-hour format
      if (timeParts[1] == 'PM' && hour < 12) hour += 12;
      if (timeParts[1] == 'AM' && hour == 12) hour = 0;

      // Schedule the daily notification - using DefaultStyleInformation to avoid BigPictureStyle
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'breathing_reminder',
        'یادآوری‌های تنفسی',
        channelDescription: 'یادآوری‌های روزانه برای تمرینات تنفسی',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        styleInformation: DefaultStyleInformation(
            true, true), // Use DefaultStyleInformation instead
      );

      const DarwinNotificationDetails iOSDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformDetails = NotificationDetails(
        android: androidDetails,
        iOS: iOSDetails,
      );

      try {
        // First try with exact timing
        await flutterLocalNotificationsPlugin.periodicallyShow(
          0,
          'یادآوری تن‌آرام',
          'لحظه‌ای وقت بگذارید و نفس عمیق بکشید',
          RepeatInterval.daily,
          platformDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );

        debugPrint(
            'Daily notification scheduled for $hour:$minute with exact timing');
      } catch (exactAlarmError) {
        // If exact alarm fails, fall back to inexact timing
        if (exactAlarmError.toString().contains('exact_alarms_not_permitted')) {
          debugPrint(
              'Exact alarms not permitted, falling back to inexact timing');

          await flutterLocalNotificationsPlugin.periodicallyShow(
            0,
            'یادآوری تن‌آرام',
            'لحظه‌ای وقت بگذارید تا نفس بکشید و آرام شوید',
            RepeatInterval.daily,
            platformDetails,
            androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
          );

          debugPrint(
              'Daily notification scheduled for $hour:$minute with inexact timing');
        } else {
          // Re-throw if it's a different error
          rethrow;
        }
      }
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
      rethrow; // Rethrow to let UI handle the error
    }
  }

  // Load data from SharedPreferences
  Future<void> _loadFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    _points = prefs.getInt('points') ?? 0;
    _notificationsEnabled = prefs.getBool('notificationsEnabled') ?? true;
    _hapticFeedbackEnabled = prefs.getBool('hapticFeedbackEnabled') ?? true;
    _dailyStreak = prefs.getInt('dailyStreak') ?? 0;
    _lastExerciseDate = prefs.getString('lastExerciseDate') ?? '';
    _completedChallenges = prefs.getStringList('completedChallenges') ?? [];
    _exercisesCompleted = prefs.getInt('exercisesCompleted') ?? 0;
    _reminderTime = prefs.getString('reminderTime');
    _dailyExerciseCount = prefs.getInt('dailyExerciseCount') ?? 0;

    // Load music settings
    _backgroundMusicEnabled = prefs.getBool('backgroundMusicEnabled') ?? false;
    _selectedMusicTrackId =
        prefs.getString('selectedMusicTrackId') ?? 'calm_lake';

    // Load challenge progress
    _dailyChallengeProgress = prefs.getInt('dailyChallengeProgress') ?? 0;
    _weeklyChallengeProgress = prefs.getInt('weeklyChallengeProgress') ?? 0;
    _dailyChallengeDate = prefs.getString('dailyChallengeDate') ?? '';
    _weeklyChallengeStartDate =
        prefs.getString('weeklyChallengeStartDate') ?? '';
    _weeklyChallengeLastUpdate =
        prefs.getString('weeklyChallengeLastUpdate') ?? '';

    // Check if we need to reset daily/weekly challenges
    _checkChallengeResets();

    // Check if we need to reset daily exercise count
    _checkAndResetDailyExerciseCount();

    // Initialize music player if needed
    if (_backgroundMusicEnabled) {
      initMusicPlayer();
    }

    notifyListeners();
  }

  // Save data to SharedPreferences
  Future<void> _saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('points', _points);
    await prefs.setBool('notificationsEnabled', _notificationsEnabled);
    await prefs.setBool('hapticFeedbackEnabled', _hapticFeedbackEnabled);
    await prefs.setInt('dailyStreak', _dailyStreak);
    await prefs.setString('lastExerciseDate', _lastExerciseDate);
    await prefs.setStringList('completedChallenges', _completedChallenges);
    await prefs.setInt('exercisesCompleted', _exercisesCompleted);
    await prefs.setInt('dailyExerciseCount', _dailyExerciseCount);
    if (_reminderTime != null) {
      await prefs.setString('reminderTime', _reminderTime!);
    }

    // Save music settings
    await prefs.setBool('backgroundMusicEnabled', _backgroundMusicEnabled);
    await prefs.setString('selectedMusicTrackId', _selectedMusicTrackId);

    // Save challenge progress
    await prefs.setInt('dailyChallengeProgress', _dailyChallengeProgress);
    await prefs.setInt('weeklyChallengeProgress', _weeklyChallengeProgress);
    await prefs.setString('dailyChallengeDate', _dailyChallengeDate);
    await prefs.setString(
        'weeklyChallengeStartDate', _weeklyChallengeStartDate);
    await prefs.setString(
        'weeklyChallengeLastUpdate', _weeklyChallengeLastUpdate);
  }

  // Check if we need to reset daily/weekly challenges
  void _checkChallengeResets() {
    final now = DateTime.now();
    final today = now.toIso8601String().split('T')[0]; // YYYY-MM-DD

    // Reset daily challenge if it's a new day
    if (_dailyChallengeDate != today) {
      _dailyChallengeProgress = 0;
      _dailyChallengeDate = today;

      // Remove daily challenge from completed challenges to allow it to be completed again
      _completedChallenges.remove('daily_challenge');
    }

    // Get the start of the current week (Monday)
    final currentWeekStart = now
        .subtract(Duration(days: now.weekday - 1))
        .toIso8601String()
        .split('T')[0];

    // Reset weekly challenge if it's a new week
    if (_weeklyChallengeStartDate != currentWeekStart) {
      _weeklyChallengeProgress = 0;
      _weeklyChallengeStartDate = currentWeekStart;
      _weeklyChallengeLastUpdate = '';
      // Remove weekly challenge from completed challenges to allow it to be completed again
      _completedChallenges.remove('weekly_challenge');
    }
  }

  // Helper method to check challenge progress
  Future<Map<String, dynamic>> getChallengeProgress(String challengeId) async {
    if (!_challengeRequirements.containsKey(challengeId)) {
      return {'completed': false, 'progress': 0, 'target': 0};
    }

    final requirement = _challengeRequirements[challengeId]!;
    final bool completed = _completedChallenges.contains(challengeId);
    int progress = 0;
    int target = requirement['target'] as int;
    String description = requirement['description'] as String? ?? '';

    switch (requirement['type']) {
      case 'streak':
        progress = _dailyStreak;
        break;
      case 'count':
        progress = _exercisesCompleted;
        break;
      case 'points':
        progress = _points;
        break;
      case 'daily':
        progress = _dailyChallengeProgress;
        description = 'امروز یک تمرین تنفسی تکمیل کنید';
        break;
      case 'weekly':
        progress = _weeklyChallengeProgress;
        description = 'این هفته ۵ تمرین تنفسی تکمیل کنید';
        break;
      case 'exercises':
        // Get the count of unique completed exercises
        final prefs = await SharedPreferences.getInstance();
        Set<String> completedExercises = Set<String>.from(
            prefs.getStringList('completedExerciseTypes') ?? []);
        progress = completedExercises.length;
        target =
            _breathingTechniques.length; // Total number of breathing techniques
        break;
    }

    return {
      'completed': completed,
      'progress': progress,
      'target': target,
      'description': description,
    };
  }

  // Synchronous version for UI that doesn't need the exact exercise count
  Map<String, dynamic> getChallengeProgressSync(String challengeId) {
    if (!_challengeRequirements.containsKey(challengeId)) {
      return {'completed': false, 'progress': 0, 'target': 0};
    }

    final requirement = _challengeRequirements[challengeId]!;
    final bool completed = _completedChallenges.contains(challengeId);
    int progress = 0;
    int target = requirement['target'] as int;
    String description = requirement['description'] as String? ?? '';

    switch (requirement['type']) {
      case 'streak':
        progress = _dailyStreak;
        break;
      case 'count':
        progress = _exercisesCompleted;
        break;
      case 'points':
        progress = _points;
        break;
      case 'daily':
        progress = _dailyChallengeProgress;
        description = 'امروز یک تمرین تنفسی تکمیل کنید';
        break;
      case 'weekly':
        progress = _weeklyChallengeProgress;
        description = 'این هفته ۵ تمرین تنفسی تکمیل کنید';
        break;
      case 'exercises':
        // For synchronous version, we'll use a cached value or a placeholder
        // This is just an estimate for UI purposes
        progress = _completedChallenges.contains('all_exercises')
            ? _breathingTechniques.length
            : (_exercisesCompleted ~/ 2).clamp(0, _breathingTechniques.length);
        target = _breathingTechniques.length;
        break;
    }

    return {
      'completed': completed,
      'progress': progress,
      'target': target,
      'description': description,
    };
  }
}
